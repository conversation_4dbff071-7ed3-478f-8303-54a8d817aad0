---
import BaseHead from '../../components/BaseHead.astro';
import Header from '../../components/Header.astro';
import Footer from '../../components/Footer.astro';
import { SITE_TITLE, SITE_DESCRIPTION } from '../../consts';
import { getCollection } from 'astro:content';
import FormattedDate from '../../components/FormattedDate.astro';
import { Image } from 'astro:assets';
import heroA from '../../assets/cooper-s-coding-notes.jpg';
import heroB from '../../assets/cooper-s-seo-summary.jpg';
import heroC from '../../assets/after-i-turn-off-alarm.webp';
import heroF from '../../assets/how-to-use-vpn-correctly.jpg';
import heroG from '../../assets/root-one-plus-8t-9008-oxgen-os.jpg';
import heroH from '../../assets/what-is-mcp.png';


const posts = (await getCollection('blog')).sort(
	(a, b) => b.data.pubDate.valueOf() - a.data.pubDate.valueOf(),
);

const { title, description, pubDate, updatedDate, heroImage } = Astro.props;
const heroMap = {
  'hero-a': heroA,
  'hero-b': heroB,
  'hero-c': heroC,
  'hero-f': heroF,
  "hero-g": heroG,
  "hero-h": heroH,
};

// 确保当前的 hero 图片与 frontmatter 中的 heroImage 对应
// The hero image mapping logic will be moved inside the map function
---

<!doctype html>
<html lang="en">
	<head>
		<!-- Google tag (gtag.js) -->
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-DKECP77S2Y"></script>
		<script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());

		gtag('config', 'G-DKECP77S2Y');
		</script>
		<BaseHead title={SITE_TITLE} description={SITE_DESCRIPTION} />
		<style>
			main {
				width: 960px;
			}
			ul {
				display: flex;
				flex-wrap: wrap;
				gap: 2rem;
				list-style-type: none;
				margin: 0;
				padding: 0;
			}
			ul li {
				width: calc(50% - 1rem);
			}
			ul li * {
				text-decoration: none;
				transition: 0.2s ease;
			}
			ul li:first-child {
				width: 100%;
				margin-bottom: 1rem;
				text-align: center;
			}
			ul li:first-child img {
				width: 100%;
			}
			ul li:first-child .title {
				font-size: 2.369rem;
			}
			ul li img {
				margin-bottom: 0.5rem;
				border-radius: 12px;
			}
			ul li a {
				display: block;
			}
			.title {
				margin: 0;
				color: var(--text-color);
				line-height: 1;
			}
			.date {
				margin: 0;
				color: var(--text-color-secondary);
			}
			ul li a:hover h4,
			ul li a:hover .date {
				color: var(--accent-color);
			}
			ul a:hover img {
				box-shadow: var(--box-shadow);
			}
			@media (max-width: 720px) {
				ul {
					gap: 0.5em;
				}
				ul li {
					width: 100%;
					text-align: center;
				}
				ul li:first-child {
					margin-bottom: 0;
				}
				ul li:first-child .title {
					font-size: 1.563em;
				}
			}
		</style>
	</head>
	<body>
		<Header />
		<main>
			<section>
				<ul>
					{
						posts.map((post) => {
							const imageSrc = heroMap[post.data.heroImage as keyof typeof heroMap];
							return (
								<li>
									<a href={`/blog/${post.id}/`}>
										{imageSrc && (
										<Image 
										src={imageSrc} 
										alt="title" 
										width={720} 
										height={360}
										loading="lazy"
										/>
										)}
										<h4 class="title">{post.data.title}</h4>
										<p class="date">
											<FormattedDate date={post.data.pubDate} />
										</p>
									</a>
								</li>
							);
						})
					}
				</ul>
			</section>
		</main>
		<Footer />
	</body>
</html>
